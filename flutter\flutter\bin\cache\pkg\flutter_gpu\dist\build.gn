# Copyright 2013 The Flutter Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//flutter/build/zip_bundle.gni")

# Similar to `sky_engine`, zip up the `flutter_gpu` Dart package and upload
# it to GCS for distribution. This is only done during Android builds on CI.
#
# Then, the Flutter tool downloads and extracts `flutter_gpu.zip` into the
# package cache (`bin/cache/pkg`) during artifact initialization.
#
# Note that unlike with `sky_engine`, the `lib/gpu` directory is already a
# complete Dart package. So no additional build steps are necessary beyond
# zipping it up.

if (is_android) {
  zip_bundle("zip") {
    output = "$android_zip_archive_dir/flutter_gpu.zip"
    files = [
      {
        source = "//flutter/lib/gpu"
        destination = "flutter_gpu"
      },
    ]
    deps = []
  }

  zip_bundle("zip_old_location") {
    # TODO: remove after we migrate the tool to use the new location.
    # Bug: https://github.com/flutter/flutter/issues/105351
    output = "flutter_gpu.zip"
    files = [
      {
        source = "//flutter/lib/gpu"
        destination = "flutter_gpu"
      },
    ]
    deps = []
  }
}

group("dist") {
  if (is_android) {
    deps = [ ":zip" ]
  }
}
